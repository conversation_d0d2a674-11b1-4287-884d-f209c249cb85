var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/app/page.js")
R.c("server/chunks/ssr/9a73b_0fd00972._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/SEO slova_seo_src_app_f5023dda._.js")
R.c("server/chunks/ssr/[root-of-the-server]__28907ea6._.js")
R.c("server/chunks/ssr/9a73b_next_dist_client_components_4c26f445._.js")
R.c("server/chunks/ssr/9a73b_next_dist_client_components_builtin_forbidden_6f2bc109.js")
R.c("server/chunks/ssr/9a73b_next_dist_client_components_builtin_unauthorized_827e7308.js")
R.c("server/chunks/ssr/9a73b_next_dist_client_components_builtin_global-error_33efbe24.js")
R.c("server/chunks/ssr/9a73b_next_dist_e8c81810._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e9bbfb5f._.js")
R.m("[project]/SEO slova/seo/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/SEO slova/seo/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/SEO slova/seo/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/SEO slova/seo/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/SEO slova/seo/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/SEO slova/seo/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/SEO slova/seo/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/SEO slova/seo/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/SEO slova/seo/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/SEO slova/seo/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/SEO slova/seo/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/SEO slova/seo/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
