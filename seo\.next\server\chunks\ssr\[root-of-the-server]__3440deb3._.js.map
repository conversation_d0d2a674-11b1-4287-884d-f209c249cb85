{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/1REACT/SEO%20slova/seo/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Cell, Tooltip } from \"recharts\";\n\nexport default function AutocompleteVisualizer() {\n  const [query, setQuery] = useState(\"\");\n  const [data, setData] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  async function fetchSuggestions() {\n    setLoading(true);\n    try {\n      const res = await fetch(\n        `https://suggestqueries.google.com/complete/search?client=firefox&q=${encodeURIComponent(\n          query\n        )}`\n      );\n      const json = await res.json();\n      const suggestions = json[1];\n      const chartData = suggestions.map((item, index) => ({\n        name: item,\n        value: 1, // all suggestions weight equally for now\n      }));\n      setData(chartData);\n    } catch (err) {\n      console.error(\"Error fetching suggestions\", err);\n    }\n    setLoading(false);\n  }\n\n  return (\n    <div className=\"p-6 max-w-2xl mx-auto\">\n      <Card className=\"mb-4\">\n        <CardContent className=\"flex gap-2 p-4\">\n          <Input\n            placeholder=\"Zadej téma (např. kávovar)\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n          />\n          <Button onClick={fetchSuggestions} disabled={loading || !query}>\n            {loading ? \"Načítám...\" : \"Vyhledat\"}\n          </Button>\n        </CardContent>\n      </Card>\n\n      {data.length > 0 && (\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"h-80\">\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <PieChart>\n                  <Pie\n                    data={data}\n                    dataKey=\"value\"\n                    nameKey=\"name\"\n                    outerRadius={120}\n                    label\n                  >\n                    {data.map((_, index) => (\n                      <Cell key={`cell-${index}`} fill={`hsl(${(index * 60) % 360}, 70%, 50%)`} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;;;;;;;;;;;;;;;;AAFA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,sOAAQ,EAAC;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,sOAAQ,EAAC,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,sOAAQ,EAAC;IAEvC,eAAe;QACb,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,MAChB,CAAC,mEAAmE,EAAE,mBACpE,QACC;YAEL,MAAM,OAAO,MAAM,IAAI,IAAI;YAC3B,MAAM,cAAc,IAAI,CAAC,EAAE;YAC3B,MAAM,YAAY,YAAY,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oBAClD,MAAM;oBACN,OAAO;gBACT,CAAC;YACD,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;QACA,WAAW;IACb;IAEA,qBACE,mQAAC;QAAI,WAAU;;0BACb,mQAAC;gBAAK,WAAU;0BACd,cAAA,mQAAC;oBAAY,WAAU;;sCACrB,mQAAC;4BACC,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;sCAE1C,mQAAC;4BAAO,SAAS;4BAAkB,UAAU,WAAW,CAAC;sCACtD,UAAU,eAAe;;;;;;;;;;;;;;;;;YAK/B,KAAK,MAAM,GAAG,mBACb,mQAAC;0BACC,cAAA,mQAAC;oBAAY,WAAU;8BACrB,cAAA,mQAAC;wBAAI,WAAU;kCACb,cAAA,mQAAC;4BAAoB,OAAM;4BAAO,QAAO;sCACvC,cAAA,mQAAC;;kDACC,mQAAC;wCACC,MAAM;wCACN,SAAQ;wCACR,SAAQ;wCACR,aAAa;wCACb,KAAK;kDAEJ,KAAK,GAAG,CAAC,CAAC,GAAG,sBACZ,mQAAC;gDAA2B,MAAM,CAAC,IAAI,EAAE,AAAC,QAAQ,KAAM,IAAI,WAAW,CAAC;+CAA7D,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kDAG9B,mQAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnB", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/1REACT/SEO%20slova/seo/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/1REACT/SEO%20slova/seo/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,0IACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/1REACT/SEO%20slova/seo/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,0IACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}