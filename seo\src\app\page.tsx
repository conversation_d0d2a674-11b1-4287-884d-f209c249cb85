"use client";

import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Cell, Tooltip } from "recharts";

export default function AutocompleteVisualizer() {
  const [query, setQuery] = useState("");
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  async function fetchSuggestions() {
    setLoading(true);
    try {
      const res = await fetch(
        `https://suggestqueries.google.com/complete/search?client=firefox&q=${encodeURIComponent(
          query
        )}`
      );
      const json = await res.json();
      const suggestions = json[1];
      const chartData = suggestions.map((item, index) => ({
        name: item,
        value: 1, // all suggestions weight equally for now
      }));
      setData(chartData);
    } catch (err) {
      console.error("Error fetching suggestions", err);
    }
    setLoading(false);
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <Card className="mb-4">
        <CardContent className="flex gap-2 p-4">
          <Input
            placeholder="Zadej téma (např. kávovar)"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <Button onClick={fetchSuggestions} disabled={loading || !query}>
            {loading ? "Načítám..." : "Vyhledat"}
          </Button>
        </CardContent>
      </Card>

      {data.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data}
                    dataKey="value"
                    nameKey="name"
                    outerRadius={120}
                    label
                  >
                    {data.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={`hsl(${(index * 60) % 360}, 70%, 50%)`} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
